import React, { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";
import { convertTo12Hour, generateTimeOptions } from "Utils/utils";
import { useClub } from "Context/Club";
import Select from "react-select";
import { useForm } from "react-hook-form";
import SportChangeModal from "./SportChangeModal";
import RightSideModal from "Components/RightSideModal";
import { AuthContext } from "Context/Auth";

const timeOptions = generateTimeOptions();

let sdk = new MkdSDK();
const ClinicDetailsModal = ({ clinic, onClose, getData, isOpen }) => {
  const defaultClinic = clinic || {
    sport_id: "",
    type: "",
    sub_type: "",
    date: "",
    start_time: "",
    end_time: "",
    name: "",
    cost_per_head: "",
    description: "",
    recurring: 0,
    id: null,
  };

  // Form control - always initialize with default values
  const { setValue, watch } = useForm({
    defaultValues: {
      sport_id: defaultClinic.sport_id,
      type: defaultClinic.type,
      sub_type: defaultClinic.sub_type,
      date: defaultClinic.date,
      start_time: defaultClinic.start_time,
      end_time: defaultClinic.end_time,
      name: defaultClinic.name,
      cost_per_head: defaultClinic.cost_per_head,
      description: defaultClinic.description,
      recurring: defaultClinic.recurring,
    },
  });

  // Basic state
  const [editLoading, setEditLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [coaches, setCoaches] = useState([]);
  const [users, setUsers] = useState([]);

  // Sport change modal state
  const [showSportChangeModal, setShowSportChangeModal] = useState(false);
  const [originalSportData, setOriginalSportData] = useState({
    sport_id: "",
    type: "",
    sub_type: "",
  });
  const [sportChangeOption, setSportChangeOption] = useState(null);

  // Sport, type, subtype state
  const [selectedSportTypes, setSelectedSportTypes] = useState([]);
  const [selectedSubTypes, setSelectedSubTypes] = useState([]);
  const [filteredEndTimeOptions, setFilteredEndTimeOptions] =
    useState(timeOptions);

  // Watch form values
  const watchSportId = watch("sport_id");
  const watchSportType = watch("type");
  const watchStartTime = watch("start_time");

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { sports } = useClub();

  // Initialize form values when clinic changes
  useEffect(() => {
    if (clinic) {
      setValue("sport_id", clinic.sport_id || "");
      setValue("type", clinic.type || "");
      setValue("sub_type", clinic.sub_type || "");
      setValue("date", clinic.date || "");
      setValue("start_time", clinic.start_time || "");
      setValue("end_time", clinic.end_time || "");
      setValue("name", clinic.name || "");
      setValue("cost_per_head", clinic.cost_per_head || "");
      setValue("description", clinic.description || "");
      setValue("recurring", clinic.recurring || 0);

      // Store original sport data for comparison
      setOriginalSportData({
        sport_id: clinic.sport_id || "",
        type: clinic.type || "",
        sub_type: clinic.sub_type || "",
      });
    }
  }, [clinic, setValue]);

  // Define getCoaches function before using it in useEffect
  const getCoaches = async () => {
    if (!defaultClinic.id) return [];

    setIsLoading(true);
    try {
      sdk.setTable("clinic_coaches");
      const response = await sdk.callRestAPI(
        { filter: [`clinic_id,eq,${defaultClinic.id}`] },
        "GETALL"
      );

      const coachesResponse = await getManyByIds(
        globalDispatch,
        authDispatch,
        "coach",
        response?.list.map((coach) => coach.coach_id),
        "user|user_id"
      );
      setCoaches(coachesResponse.list);
    } catch (error) {
      console.log(error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch coaches when clinic ID changes
  useEffect(() => {
    if (clinic?.id) {
      getCoaches();
    }
  }, [clinic?.id]); // Only re-fetch coaches when clinic ID changes, not on every clinic prop change

  // Update available types when sport changes
  useEffect(() => {
    if (watchSportId) {
      const selectedSport = sports.find(
        (sport) => sport.id.toString() === watchSportId.toString()
      );
      if (selectedSport) {
        // Filter out empty types (where type is an empty string)
        const validSportTypes =
          selectedSport.sport_types?.filter(
            (sportType) => sportType.type !== ""
          ) || [];
        setSelectedSportTypes(validSportTypes);
      } else {
        setSelectedSportTypes([]);
      }
    } else {
      setSelectedSportTypes([]);
    }
  }, [watchSportId, sports]);

  // Update available subtypes when type changes
  useEffect(() => {
    if (watchSportType) {
      const selectedType = selectedSportTypes.find(
        (type) => type.type === watchSportType
      );
      if (selectedType) {
        // Filter out empty subtypes
        const validSubtypes = (selectedType.subtype || []).filter(
          (subtype) => subtype !== ""
        );
        setSelectedSubTypes(validSubtypes);
      } else {
        setSelectedSubTypes([]);
      }
    } else {
      setSelectedSubTypes([]);
    }
  }, [watchSportType, selectedSportTypes]);

  // Function to filter time options based on start time
  const getFilteredTimeOptions = (startTime) => {
    if (!startTime) return timeOptions;

    // Find the index of the selected start time
    const startTimeIndex = timeOptions.findIndex(
      (option) => option.value === startTime
    );

    if (startTimeIndex === -1) return timeOptions;

    // Return only options that come after the selected start time
    return timeOptions.filter((_, index) => index > startTimeIndex);
  };

  // Update end time options when start time changes
  useEffect(() => {
    if (watchStartTime) {
      // Filter end time options to only show times after the selected start time
      const filteredOptions = getFilteredTimeOptions(watchStartTime);
      setFilteredEndTimeOptions(filteredOptions);
    } else {
      // If no start time is selected, show all options
      setFilteredEndTimeOptions(timeOptions);
    }
  }, [watchStartTime]);

  // Removed the conditional return that was here

  const refreshCoachesData = async () => {
    const updatedCoaches = await getCoaches();
    if (updatedCoaches) {
      setCoaches(updatedCoaches);
    }
  };

  // Function to handle saving all fields at once
  const handleSaveAll = async () => {
    if (!defaultClinic.id) {
      showToast(
        globalDispatch,
        "Cannot save: no clinic selected",
        3000,
        "error"
      );
      return;
    }

    setEditLoading(true);
    try {
      // Get all form values
      const formData = {
        id: defaultClinic.id,
        name: watch("name"),
        cost_per_head: parseFloat(watch("cost_per_head")),
        description: watch("description"),
        sport_id: watch("sport_id"),
        type: watch("type"),
        sub_type: watch("sub_type"),
        date: watch("date"),
        start_time: watch("start_time"),
        end_time: watch("end_time"),
        recurring:
          watch("recurring") === 1 || watch("recurring") === true ? 1 : 0,
      };

      // Check if sport/type/subtype has changed
      const sportChanged =
        formData.sport_id !== originalSportData.sport_id ||
        formData.type !== originalSportData.type ||
        formData.sub_type !== originalSportData.sub_type;

      // If sport/type/subtype changed and we have a selected option, include it in the payload
      if (sportChanged && sportChangeOption !== null) {
        formData.sport_change_option = sportChangeOption;
      }

      sdk.setTable("clinics");
      const response = await sdk.callRestAPI(formData, "PUT");

      if (!response?.error) {
        showToast(
          globalDispatch,
          "Clinic updated successfully",
          3000,
          "success"
        );

        // Update the local clinic data with all new values
        if (clinic) {
          Object.keys(formData).forEach((key) => {
            if (key !== "id") {
              clinic[key] = formData[key];
            }
          });
        }

        // Exit edit mode
        setIsEditing(false);

        // Refresh coaches data
        await refreshCoachesData();

        // Refresh the data in the parent component
        getData();
      }
    } catch (error) {
      showToast(
        globalDispatch,
        error?.message || "An error occurred",
        3000,
        "error"
      );
      console.log(error);
    } finally {
      setEditLoading(false);
    }
  };

  const returnUser = (id) => {
    const user = users.find((user) => user.id === id);
    return user ? `${user.first_name} ${user.last_name}` : "Not assigned";
  };

  // Make sure form values are updated when entering edit mode
  useEffect(() => {
    if (isEditing) {
      // Ensure all form values are set correctly
      setValue("name", defaultClinic.name || "");
      setValue("cost_per_head", defaultClinic.cost_per_head || "");
      setValue("description", defaultClinic.description || "");
      setValue("sport_id", defaultClinic.sport_id || "");
      setValue("type", defaultClinic.type || "");
      setValue("sub_type", defaultClinic.sub_type || "");
      setValue("date", defaultClinic.date || "");
      setValue("start_time", defaultClinic.start_time || "");
      setValue("end_time", defaultClinic.end_time || "");
      setValue("recurring", defaultClinic.recurring);
    }
  }, [isEditing, defaultClinic, setValue]);

  // Don't render anything if not open
  if (!isOpen) {
    return null;
  }

  return (
    <>
      <RightSideModal
        isOpen={isOpen}
        onClose={onClose}
        title={defaultClinic.name || "Clinic details"}
        showFooter={false}
      >
        <div className="">
          {/* Edit/Save/Cancel buttons */}
          <div className="mb-6 flex justify-end">
            {isEditing ? (
              <div className="flex gap-2">
                <button
                  onClick={() => setIsEditing(false)}
                  className="rounded-xl border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <InteractiveButton
                  loading={editLoading}
                  onClick={handleSaveAll}
                  className="rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800"
                >
                  Save All Changes
                </InteractiveButton>
              </div>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800"
              >
                Edit All
              </button>
            )}
          </div>

          {/* Name */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Name</span>
            </div>
            {isEditing ? (
              <input
                type="text"
                value={watch("name") || ""}
                onChange={(e) => setValue("name", e.target.value)}
                className="mt-1 w-full rounded-xl border border-gray-300 p-2"
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.name}
              </div>
            )}
          </div>

          {/* Cost per person */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Cost per person</span>
            </div>
            {isEditing ? (
              <input
                type="number"
                value={watch("cost_per_head") || ""}
                onChange={(e) => setValue("cost_per_head", e.target.value)}
                className="mt-1 w-full rounded-xl border border-gray-300 p-2"
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {fCurrency(defaultClinic.cost_per_head)}
              </div>
            )}
          </div>

          {/* Description */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Description</span>
            </div>
            {isEditing ? (
              <textarea
                value={watch("description") || ""}
                onChange={(e) => setValue("description", e.target.value)}
                className="mt-1 w-full rounded-xl border border-gray-300 p-2"
                rows={3}
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.description || "No description provided"}
              </div>
            )}
          </div>

          {/* Sport */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Sport</span>
            </div>
            {isEditing ? (
              <Select
                className="mt-1 w-full rounded-lg text-sm"
                options={sports
                  .filter((sport) => sport.status === 1)
                  .map((sport) => ({
                    value: sport.id.toString(),
                    label: sport.name,
                  }))}
                value={{
                  value: watchSportId,
                  label:
                    sports.find((s) => s.id.toString() == watchSportId)?.name ||
                    "Select sport",
                }}
                onChange={(option) => {
                  // Check if sport has changed
                  if (option.value !== originalSportData.sport_id) {
                    // Show confirmation modal
                    setShowSportChangeModal(true);
                  } else {
                    // If same sport, just update the value
                    setValue("sport_id", option.value);
                    setValue("type", "");
                    setValue("sub_type", "");
                  }
                }}
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {sports.find((s) => s.id.toString() == defaultClinic.sport_id)
                  ?.name || "No sport selected"}
              </div>
            )}
          </div>

          {/* Type */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Type</span>
            </div>
            {isEditing ? (
              <>
                {selectedSportTypes.length > 0 ? (
                  <Select
                    className="mt-1 w-full rounded-lg text-sm"
                    options={selectedSportTypes.map((type) => ({
                      value: type.type,
                      label: type.type,
                    }))}
                    value={{
                      value: watchSportType,
                      label: watchSportType || "Select type",
                    }}
                    onChange={(option) => {
                      // Check if type has changed and sport is the same
                      if (
                        option.value !== originalSportData.type &&
                        watchSportId === originalSportData.sport_id
                      ) {
                        // Show confirmation modal
                        setShowSportChangeModal(true);
                      } else {
                        // If same type or sport already changed, just update the value
                        setValue("type", option.value);
                        setValue("sub_type", "");
                      }
                    }}
                  />
                ) : (
                  <div className="mt-1 text-sm italic text-gray-500">
                    This sport has no types
                  </div>
                )}
              </>
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.type || "No type selected"}
              </div>
            )}
          </div>

          {/* Sub-type */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Sub-type</span>
            </div>
            {isEditing ? (
              <>
                {selectedSubTypes.length > 0 ? (
                  <Select
                    className="mt-1 w-full rounded-lg text-sm"
                    options={selectedSubTypes.map((subtype) => ({
                      value: subtype,
                      label: subtype,
                    }))}
                    value={{
                      value: watch("sub_type"),
                      label: watch("sub_type") || "Select sub-type",
                    }}
                    onChange={(option) => {
                      // Check if subtype has changed and sport/type are the same
                      if (
                        option.value !== originalSportData.sub_type &&
                        watchSportId === originalSportData.sport_id &&
                        watchSportType === originalSportData.type
                      ) {
                        // Show confirmation modal
                        setShowSportChangeModal(true);
                      } else {
                        // If same subtype or sport/type already changed, just update the value
                        setValue("sub_type", option.value);
                      }
                    }}
                  />
                ) : (
                  <div className="mt-1 text-sm italic text-gray-500">
                    This type has no sub-types
                  </div>
                )}
              </>
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.sub_type || "No sub-type selected"}
              </div>
            )}
          </div>

          {/* Date */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Date</span>
            </div>
            {isEditing ? (
              <input
                type="date"
                value={watch("date") || ""}
                onChange={(e) => setValue("date", e.target.value)}
                className="mt-1 w-full rounded-xl border border-gray-300 p-2"
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.date}
              </div>
            )}
          </div>

          {/* Start time */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Start time</span>
            </div>
            {isEditing ? (
              <Select
                className="mt-1 w-full rounded-lg text-sm"
                options={timeOptions}
                value={{
                  value: watch("start_time"),
                  label: convertTo12Hour(watch("start_time")) || "Select time",
                }}
                onChange={(option) => {
                  setValue("start_time", option.value);
                }}
                placeholder="Select start time"
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {convertTo12Hour(defaultClinic.start_time) || "Not set"}
              </div>
            )}
          </div>

          {/* End time */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">End time</span>
            </div>
            {isEditing ? (
              <Select
                className="mt-1 w-full rounded-lg text-sm"
                options={filteredEndTimeOptions}
                value={{
                  value: watch("end_time"),
                  label: convertTo12Hour(watch("end_time")) || "Select time",
                }}
                onChange={(option) => {
                  setValue("end_time", option.value);
                }}
                placeholder={
                  !watchStartTime
                    ? "Select start time first"
                    : "Select end time"
                }
                isDisabled={!watchStartTime}
              />
            ) : (
              <div className="mt-1 font-medium text-black">
                {convertTo12Hour(defaultClinic.end_time) || "Not set"}
              </div>
            )}
          </div>

          {/* Recurring */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Recurring</span>
            </div>
            {isEditing ? (
              <select
                value={
                  watch("recurring") === 1 || watch("recurring") === true
                    ? "Yes"
                    : "No"
                }
                onChange={(e) =>
                  setValue("recurring", e.target.value === "Yes" ? 1 : 0)
                }
                className="mt-1 w-full rounded-xl border border-gray-300 p-2"
              >
                <option value="Yes">Yes</option>
                <option value="No">No</option>
              </select>
            ) : (
              <div className="mt-1 font-medium text-black">
                {defaultClinic.recurring === 1 ? "Yes" : "No"}
              </div>
            )}
          </div>

          {/* Coaches section */}
          <div>
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Coaches</span>
              </div>
              <div className="mt-1 font-medium text-black">
                {coaches.length > 0 ? (
                  coaches.map((coach) => (
                    <div key={coach.id} className="py-1">
                    
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500">No coaches assigned</div>
                )}
              </div>
            </div>
          </div>

          {isLoading && <LoadingSpinner />}
        </div>
      </RightSideModal>{" "}
      {/* Render the sport change modal when needed */}
      {showSportChangeModal && (
        <SportChangeModal
          onClose={() => {
            // Reset to original values
            setValue("sport_id", originalSportData.sport_id);
            setValue("type", originalSportData.type);
            setValue("sub_type", originalSportData.sub_type);

            // Reset option and close modal
            setSportChangeOption(null);
            setShowSportChangeModal(false);
          }}
          onConfirm={(option) => {
            // Set the selected option in the parent component
            setSportChangeOption(parseInt(option));

            // Apply the sport/type/subtype changes
            const newSportId = watch("sport_id");
            const newType = watch("type");
            const newSubType = watch("sub_type");

            // Update form values
            setValue("sport_id", newSportId);
            setValue("type", newType);
            setValue("sub_type", newSubType);

            // Close the modal
            setShowSportChangeModal(false);
          }}
          eventCounts={{
            total: 12, // Example data - in a real app, this would be fetched from the API
            completed: 4,
            upcoming: "April 3, 2025", // Next upcoming event date
            lastEvent: "June 19, 2025", // Last event date
          }}
        />
      )}
    </>
  );
};

export default ClinicDetailsModal;
